# 轻聆 App - 综合开发流程指导文档

**版本**: 2.0
**日期**: 2024-12-19
**重构状态**: ✅ 已完成编译错误修复和架构优化
**项目状态**: ✅ 可编译运行，符合MVVM架构规范

## 目录
1.  [引言](#1-引言)
2.  [核心开发规范与沟通准则](#2-核心开发规范与沟通准则)
3.  [应用整体架构概述](#3-应用整体架构概述)
    *   3.1. [MVVM 架构](#31-mvvm-架构)
    *   3.2. [模块化建议](#32-模块化建议)
    *   3.3. [统一播放服务 (`UnifiedPlaybackService`)](#33-统一播放服务-unifiedplaybackservice)
4.  [应用整体流程图 (Mermaid)](#4-应用整体流程图-mermaid)
5.  [核心流程详解 (包含交互细节)](#5-核心流程详解-包含交互细节)
    *   5.1. [启动与欢迎 (`SplashActivity`)](#51-启动与欢迎-splashactivity)
    *   5.2. [登录状态检查](#52-登录状态检查)
    *   5.3. [登录操作 (`LoginActivity`)](#53-登录操作-loginactivity)
    *   5.4. [进入主界面 (`MainActivity`)](#54-进入主界面-mainactivity)
    *   5.5. [主界面交互 (`MainActivity`)](#55-主界面交互-mainactivity)
6.  [各功能视图详解 (布局与交互)](#6-各功能视图详解-布局与交互)
    *   6.1. [播放器视图 (`PlayerFragment`)](#61-播放器视图-playerfragment)
    *   6.2. [我的音乐库视图 (`MusicLibraryFragment`)](#62-我的音乐库视图-musiclibraryfragment)
    *   6.3. [音乐探索视图 (`DiscoveryFragment`)](#63-音乐探索视图-discoveryfragment)
    *   6.4. [驾驶模式视图 (`DrivingModeFragment`)](#64-驾驶模式视图-drivingmodefragment)
    *   6.5. [用户中心视图 (`UserProfileFragment`)](#65-用户中心视图-userprofilefragment)
    *   6.6. [应用设置视图 (`SettingsFragment`)](#66-应用设置视图-settingsfragment)
7.  [API 接口集成与数据处理](#7-api-接口集成与数据处理)
8.  [UI/UX 设计与资源](#8-uiux-设计与资源)
9.  [性能与健壮性](#9-性能与健壮性)
10. [GitHub 项目借鉴分析与主动学习](#10-github-项目借鉴分析与主动学习)
11. [AI 辅助开发要求](#11-ai-辅助开发要求)

---

## 1. 引言
本文档为"轻聆"智能车载音乐播放器项目的综合开发流程指导。旨在为开发者和AI辅助工具提供一个清晰、统一的开发蓝图，确保项目按预期功能和质量完成。请严格遵循本文档中的流程、规范和功能描述。

---

## 2. 核心开发规范与沟通准则

*   **响应语言**: AI 必须始终使用 **中文** 进行所有交流、代码注释。
*   **文件完整性审查**: 在完成每个页面或主要步骤的编码后，开发者和AI都应仔细对照该页面的"核心组件/文件"列表以及实现细节，审查是否遗漏了某些文件的编写或修改。
*   **语音模式功能**: 现有语音模式相关代码（若已提供）应尽量避免修改，AI应专注于将其平滑集成接入新功能中。
*   **冗余文件清理**: 在开发过程中，AI 可以主动识别或在开发者指示下，删除项目中任何确认不再使用或多余的文件。
*   **开发语言**: 项目必须 **严格使用纯 Java 语言** 进行开发。
*   **代码风格与注释**:
    *   严格遵循标准的 Java 编码规范和 Android 开发最佳实践。
    *   代码应易于理解、清晰明了，包括合理的命名、适当的缩进和格式化。
    *   **JavaDoc 注释**: 必须为所有主要类、公共方法、复杂逻辑块添加清晰、准确的 JavaDoc 注释。
    *   **XML 视图 ID**: XML 布局文件中的视图 ID 应清晰、有意义，并遵循统一的命名约定（例如 `button_submit`, `textview_username`）。
*   **功能开发范围与流程遵循**:
    *   **严格遵循需求**: AI 在进行任何功能开发时，必须严格遵循用户（开发者）提供的功能需求说明、设计规格以及预定的业务逻辑流程。
    *   **禁止超范围开发**: 严禁 AI 实现任何未经确认的功能。AI 的核心任务是精确、高效地实现既定需求。若有更好建议，应先提出并等待用户确认。

---

## 3. 应用整体架构概述

### 3.1. MVVM 架构
建议采用MVVM (Model-View-ViewModel) 架构模式，以实现UI逻辑与业务逻辑的有效分离。
*   **View**: `Activity` 和 `Fragment`，负责UI展示和用户输入捕获。
*   **ViewModel**: 负责为View提供其所需的数据，并处理View的交互逻辑。ViewModel不应直接持有View的引用。通过`LiveData`或`StateFlow` (若使用Kotlin Flow，但本项目要求Java，故主要考虑`LiveData`或自定义观察者模式)暴露数据给View。
*   **Model**: 数据层，包括数据仓库 (`Repository`)、数据源 (本地数据库如Room，网络API如Retrofit，以及统一播放服务)。

### 3.2. 模块化建议
考虑将应用拆分为多个模块，以提高代码复用性和可维护性，借鉴`NeteaseCloudMusic-MVVM-master`的实践：
*   `app`: 主应用模块，整合所有功能。
*   `lib_audio`: 核心音频播放服务模块 (包含`UnifiedPlaybackService`)。
*   `lib_network`: 网络请求模块 (Retrofit, OkHttp实现)。
*   `lib_common_ui`: 通用UI组件和工具类。
*   `lib_data`: 数据模型、Repository、本地存储等。

### 3.3. 统一播放服务 (`UnifiedPlaybackService`)
*   **定位**: 后台`Service`，应用内所有音乐播放的核心管理者，独立于UI生命周期。
*   **职责**:
    *   管理播放队列（添加、移除、排序歌曲）。
    *   控制音乐播放：使用`ExoPlayer` (推荐) 或 `MediaPlayer` 实现播放、暂停、停止、上一首、下一首、Seek。
    *   实现四种播放模式逻辑：顺序播放、列表循环、单曲循环、随机播放 (参考 `car.html` 中的模式)。
    *   维护和通过`LiveData`或自定义观察者模式广播当前播放状态：当前歌曲对象、播放进度、缓冲进度、播放/暂停状态、当前播放模式、错误状态。
    *   处理音频焦点 (`AudioManager.OnAudioFocusChangeListener`)。
    *   响应耳机插拔、蓝牙断开连接等系统广播。
    *   管理媒体通知 (`Notification`)，允许通过通知栏控制播放（需要`FOREGROUND_SERVICE`权限及Android 13+的`POST_NOTIFICATIONS`权限）。
    *   提供Binder接口或类似机制供`Activity`/`ViewModel`绑定服务、发送播放命令、注册状态监听。
*   **交互**: `PlayerFragment`、`DrivingModeFragment`以及列表中的播放操作均通过`ViewModel`与此服务通信，而不是直接控制播放器。
    *   **推荐通信机制**:
        *   **Service -> ViewModel**: `UnifiedPlaybackService` 内部维护关键状态的 `LiveData` 对象（如当前歌曲 `LiveData<Song>`, 播放状态 `LiveData<Boolean>`, 播放进度 `LiveData<Pair<Long, Long>>`, 播放模式 `LiveData<PlayMode>`, 播放列表 `LiveData<List<Song>>` 等）。
        *   **ViewModel -> Service**: `Activity`/`Fragment` 中的 `ViewModel` 持有到 `UnifiedPlaybackService` 的 Binder 引用（通过 `bindService` 获取）。`ViewModel` 提供公共方法（如 `playPause()`, `next()`, `seekTo(pos)`)，内部通过 Binder 调用 Service 的对应方法。
        *   **View <-> ViewModel**: `Fragment`/`Activity` (View) 观察 `ViewModel` 中的 `LiveData` 以更新 UI。用户的交互（如点击按钮）调用 `ViewModel` 的公共方法。
*   **绑定与解绑**: `MainActivity` 或各相关 `Fragment` 的 `ViewModel` 负责在其生命周期内（例如 `onStart`/`onStop` 或 `onCreate`/`onDestroy`，取决于需求）绑定和解绑 `UnifiedPlaybackService`，并获取 Binder 实例。确保在不需要时正确解绑，防止服务泄露。

### 3.4. 建议开发优先级与依赖关系

1.  **阶段一：基础架构与核心播放 (优先级最高)**
    *   **目标**: 搭建项目骨架，实现最基本的音乐播放功能。
    *   **任务**:
        *   创建项目，设置基础模块 (`app`, `lib_audio`, `lib_network`, `lib_common_ui`, `lib_data`)。
        *   实现 `UnifiedPlaybackService` 核心逻辑：
            *   集成 `ExoPlayer` 或 `MediaPlayer`。
            *   实现基本的播放、暂停、停止、下一首、上一首功能。
            *   通过 `LiveData` 暴露基础播放状态（当前歌曲名、播放/暂停状态）。
        *   创建 `MainActivity` 骨架，包含 `FragmentContainerView` 和基础的侧边栏导航逻辑 (无需完整样式)。
        *   创建 `PlayerFragment` 骨架，能绑定到 `UnifiedPlaybackService`，显示基础状态，并能触发播放/暂停。
    *   **依赖**: 此阶段是后续所有功能的基础。

2.  **阶段二：核心流程与基础 UI (优先级高)**
    *   **目标**: 完成应用的基本启动、登录流程，完善播放器界面。
    *   **任务**:
        *   实现 `SplashActivity` 和 `LoginActivity` (根据文档要求，集成现有UI，处理登录逻辑和状态检查)。
        *   完善 `PlayerFragment` UI (参考布局要求，显示封面、歌词占位、完整控制按钮、进度条)。
        *   实现 `PlayerFragment` 与 `UnifiedPlaybackService` 的完整双向通信 (通过 `ViewModel` 和 `LiveData`)，同步所有播放状态。
        *   实现 `MainActivity` 的侧边栏导航交互，能够切换到 `PlayerFragment`。
    *   **依赖**: 依赖阶段一完成。

3.  **阶段三：主要功能模块 - 本地优先 (优先级中)**
    *   **目标**: 实现本地音乐库的浏览和播放。
    *   **任务**:
        *   实现 `MusicLibraryFragment` 框架，包含 `TabLayout` 和 `ViewPager2`。
        *   创建 "本地音乐" 对应的 `LocalMusicListFragment`：
            *   实现本地音频文件扫描逻辑。
            *   使用 `RecyclerView` 展示本地音乐列表。
            *   实现列表项点击播放（通过 `UnifiedPlaybackService`）。
            *   实现基础的"更多操作"（如播放下一首）。
        *   完善 `MainActivity` 侧边栏，允许导航到 `MusicLibraryFragment`。
    *   **依赖**: 依赖阶段一、二完成。本地功能不依赖 API。

4.  **阶段四：主要功能模块 - API 依赖 (优先级中)**
    *   **目标**: 实现依赖网络 API 的核心功能模块。
    *   **任务**: (根据 `api.txt` 接口可用性逐步实现)
        *   实现 `lib_network` 模块，配置 Retrofit/OkHttp。
        *   实现 `MusicLibraryFragment` 中的云端数据标签页（歌曲、专辑、歌手、歌单）的数据获取与展示。
        *   实现 `DiscoveryFragment` 的布局和各分区内容的 API 数据获取与展示。
        *   实现 `UserProfileFragment` 的 API 数据获取与展示。
        *   实现各模块中涉及 API 的"更多操作"（收藏、添加到歌单等）。
    *   **依赖**: 依赖阶段一、二完成。`lib_network` 模块需优先完成。各项功能的实现严格依赖 `api.txt` 中对应接口的可用性。

5.  **阶段五：特定功能与优化 (优先级低)**
    *   **目标**: 实现驾驶模式、设置，并进行整体优化。
    *   **任务**:
        *   实现 `DrivingModeFragment` 的布局与交互逻辑，包括语音交互集成（若有）。
        *   实现 `SettingsFragment` 的布局与交互逻辑。
        *   完善权限处理逻辑。
        *   实现全屏模式。
        *   添加加载动画、过渡效果。
        *   进行性能优化（内存、UI流畅度）。
        *   添加详细的错误处理和用户提示。
    *   **依赖**: 依赖前序阶段完成。

---

## 4. 应用整体流程图 (Mermaid)

```mermaid
graph TD
    A[启动应用] --> B[SplashActivity: 显示欢迎页];
    B -- 短暂显示后 --> C{检查持久化登录状态};
    C -- 已登录 --> E_PrepareMain[准备进入主界面];
    C -- 未登录 --> D[LoginActivity: 显示登录选项];

    subgraph LoginActivity Process
        D --> D_QR[按钮: 二维码登录];
        D --> D_Phone[按钮: 手机号登录];
        D --> D_Guest[按钮: 游客登录];

        D_QR -- 点击 --> D_QR_Dialog[弹出 QrLoginDialog];
        D_Phone -- 点击 --> D_Phone_Dialog[弹出 PhoneLoginDialog];

        D_QR_Dialog -- 登录成功 (需API) --> LoginSuccess;
        D_Phone_Dialog -- 登录成功 (需API) --> LoginSuccess;

        D_Guest -- 点击 --> CallGuestLoginAPI[调用游客登录API (必须成功)];
        CallGuestLoginAPI -- API调用成功 --> LoginSuccess;
        CallGuestLoginAPI -- API缺失/调用失败 --> GuestLoginError[显示错误弹窗说明, 停在登录页];
        GuestLoginError --> D;

        LoginSuccess[登录成功/游客API成功进入] -- 保存状态 --> E_PrepareMain;
        D_QR_Dialog -- 关闭/失败 --> LoginFailDialog[显示错误弹窗说明];
        D_Phone_Dialog -- 关闭/失败 --> LoginFailDialog;
        LoginFailDialog --> D;
    end

    E_PrepareMain --> E_StartActivity[启动 MainActivity];

    subgraph MainActivity Lifecycle and Permissions
        E_StartActivity --> E_OnCreate[MainActivity onCreate];
        E_OnCreate --> E_CheckPerms{检查运行时权限? (存储/媒体, 录音...)};
        E_CheckPerms -- 未授予 --> E_RequestPerms[请求权限];
        E_RequestPerms -- 用户响应 --> E_HandlePermResult{处理权限结果};
        E_HandlePermResult -- 拒绝 --> E_ShowRationaleOrBlock[显示理由/阻止功能/退出?];
        E_CheckPerms -- 已授予 --> E_LoadDefaultView[加载默认视图 PlayerFragment];
        E_HandlePermResult -- 授予 --> E_LoadDefaultView;
    end

    subgraph MainActivity with Sidebar Navigation
        E_LoadDefaultView --> E_Base[MainActivity 核心];
        E_Base --> Sidebar[侧边导航栏 (选中项高亮, 可自动隐藏/滑出)];
        E_Base --> ContentArea[Fragment容器];

        Sidebar -- 点击 '播放器' --> E_Player[PlayerFragment 显示在 ContentArea];
        Sidebar -- 点击 '我的音乐库' --> E_Library[MusicLibraryFragment 显示在 ContentArea];
        Sidebar -- 点击 '音乐探索' --> E_Discovery[DiscoveryFragment 显示在 ContentArea];
        Sidebar -- 点击 '驾驶模式' --> E_Driving[DrivingModeFragment 显示在 ContentArea];
        Sidebar -- 点击 '用户中心' --> E_Profile[UserProfileFragment 显示在 ContentArea];
        Sidebar -- 点击 '设置' --> E_Settings[SettingsFragment 显示在 ContentArea];
    end

    subgraph PlayerFragment Details
        E_Player --> PF_ServiceInteraction[与统一播放服务交互];
        E_Player --> PF_Info[左: ImageView封面, TextView歌名/歌手];
        E_Player --> PF_Lyrics[右: LyricsView/RecyclerView歌词];
        E_Player --> PF_Controls[右下: ImageButtons控制面板, SeekBar进度, TextView时间];
    end

    subgraph MusicLibraryFragment Details
        E_Library --> ML_ServiceInteraction[与统一播放服务交互 (用于播放)];
        E_Library --> ML_Search[顶部: SearchView];
        E_Library --> ML_Tabs[中部: TabLayout + ViewPager2 (本地, 歌曲, 专辑, 歌手, 歌单)];
        ML_Tabs -- 选择Tab --> ML_List[下部: RecyclerView + Item布局 + 更多操作Dialog/BottomSheet];
    end

    subgraph MusicDiscoveryFragment Details
        E_Discovery --> Disc_ServiceInteraction[与统一播放服务交互 (用于播放)];
        E_Discovery --> Disc_Search[顶部: SearchView];
        E_Discovery --> Disc_Sections[内容: RecyclerView (嵌套横向RV) + Card布局 + 分区Header(含更多按钮)];
        Disc_Sections -- 点击 --> Disc_ItemInteraction[播放/详情/更多操作Dialog];
    end

    subgraph DrivingModeFragment Details
        E_Driving --> DM_CheckAutoVoice[检查设置: 是否自动开启语音?];
        DM_CheckAutoVoice -- 是 --> DM_ActivateVoice[自动激活语音];
        DM_CheckAutoVoice -- 否 --> DM_NormalStart;
        DM_ActivateVoice --> DM_NormalStart[正常UI加载];
        DM_NormalStart --> DM_ServiceInteraction[与统一播放服务交互];
        DM_NormalStart --> DM_PlayerInfo[左: 大ImageView封面, 大TextView歌名/歌手, 大SeekBar];
        DM_NormalStart --> DM_QuickActions[右: GridLayout(大ImageButton + TextView)];
    end

    subgraph UserProfileFragment Details
        E_Profile --> UP_Header[顶部: ImageView背景, 大圆ImageView头像, TextViews(昵称/标签/签名)];
        E_Profile --> UP_Stats[中部: CardView包含LinearLayout(TextViews统计数据)];
        E_Profile --> UP_AccountInfo[下部: LinearLayout/RecyclerView(TextView标签+值信息)];
    end

    E_Settings --> Set_Options[RecyclerView/PreferenceFragmentCompat(包含3个SwitchPreference项)];
```

---

## 5. 核心流程详解 (包含交互细节)

### 5.1. 启动与欢迎 (`SplashActivity`)
*   **功能**: 应用首次启动时展示欢迎界面。
*   **布局**: 全屏，无顶部标题栏。中心区域显示应用Logo、名称"轻聆"和Slogan"您的专属智能车载音乐伴侣"。
*   **核心组件**: `SplashActivity.java`, `activity_splash.xml`, Logo Drawable。
*   **流程**: 短暂显示（例如2-3秒）后，自动进行下一步的登录状态检查。

### 5.2. 登录状态检查
*   **执行者**: `SplashActivity`或其调用的工具类。
*   **逻辑**: 检查本地持久化存储（如`SharedPreferences`）中是否存在有效的用户登录凭证（如Token）。
    *   **若已登录**: 直接准备启动`MainActivity`。
    *   **若未登录**: 跳转至`LoginActivity`。

### 5.3. 登录操作 (`LoginActivity`)
*   **功能**: 处理用户登录，提供多种登录方式。
*   **布局 (`activity_login.xml`)**: 全屏，无顶部标题栏。居中或合理区域放置登录选项按钮。
    *   按钮1: "二维码登录" (id: `button_qr_login`)
    *   按钮2: "手机号登录" (id: `button_phone_login`)
    *   按钮3: "游客登录" (id: `button_guest_login`)
*   **核心组件**: `LoginActivity.java`, `QrLoginDialog.java` (及布局 `dialog_qr_login.xml`), `PhoneLoginDialog.java` (及布局 `dialog_phone_login.xml`)。
*   **流程与交互**:
    *   **二维码/手机号登录**: 点击对应按钮后，分别弹出一个模态的自定义`Dialog` (`QrLoginDialog` / `PhoneLoginDialog`)。用户在Dialog内完成扫码或输入手机号、验证码操作。登录过程需与后端API交互验证。
        *   **登录失败处理**: 若API验证失败、网络错误或用户关闭Dialog，**必须弹出一个提示`Dialog`或`Toast`向用户明确说明登录失败的原因**（如"二维码已过期"、"验证码错误"、"网络连接失败"），然后用户停留在`LoginActivity`，Dialog关闭。
    *   **游客登录**: 用户点击后，应用**必须**尝试调用`api.txt`中定义的游客登录接口。
        *   **API缺失/调用失败处理**: 若`api.txt`中未定义此接口，或接口调用失败，**必须弹出一个提示`Dialog`或`Toast`明确告知用户游客登录不可用或失败原因**，用户停留在`LoginActivity`。
    *   **登录成功**: 任意方式成功登录后，应用需保存用户凭证和登录状态，然后准备启动`MainActivity`。

### 5.4. 进入主界面 (`MainActivity`)
*   **触发**: `LoginActivity`成功后，或`SplashActivity`检查到已登录状态。
*   **核心动作**:
    1.  启动`MainActivity`。
    2.  `MainActivity`在其`onCreate`或`onResume`生命周期方法中，**必须首先检查并请求应用运行所需的核心运行时权限**。这些权限至少包括：
        *   存储/媒体访问权限 (例如 `READ_MEDIA_AUDIO` for Android 13+, or `READ_EXTERNAL_STORAGE` for older versions)。
        *   录音权限 (`RECORD_AUDIO`)，用于语音控制功能。
        *   通知权限 (`POST_NOTIFICATIONS`)，用于Android 13+的后台播放通知。
    3.  **权限请求流程**:
        *   检查权限是否已授予。
        *   若未授予，则调用系统API发起权限请求。
        *   处理`onRequestPermissionsResult`回调：
            *   若用户授予所有关键权限，则继续。
            *   若用户拒绝一项或多项关键权限，应根据权限重要性：
                *   显示解释性对话框 (Rationale Dialog) 说明为何需要此权限，并提供再次请求的选项。
                *   如果用户持续拒绝核心权限（如存储读取对音乐播放至关重要），则可能需要提示用户应用无法正常运行，并考虑禁用相关功能或引导用户到设置页手动开启，极端情况可考虑退出应用。
    4.  **在确认所有核心权限均已授予后**，`MainActivity`**默认加载`PlayerFragment`**作为其内容区域的初始视图。

### 5.5. 主界面交互 (`MainActivity`)
*   **布局 (`activity_main.xml`)**: 全屏，无顶部标题栏。
    *   **侧边导航栏 (`Sidebar`)**: 通常位于屏幕左侧。
        *   **实现**: 可使用`RecyclerView`或自定义垂直`LinearLayout`。导航项包含图标（`ImageView`）和可选的简短文字（`TextView`）。原型以图标为主。
        *   **导航项**: 播放器、我的音乐库、音乐探索、驾驶模式、用户中心、设置。
        *   **高亮状态**: 当前选中的导航项**必须有明确的视觉高亮**（例如，背景色变化、图标颜色变化，或左侧/右侧有指示条）。
        *   **自动隐藏/滑出**: 在用户一段时间无操作后（例如10-15秒），导航栏自动向左平滑收起。用户可从屏幕左边缘向右滑动以重新呼出，或提供一个小的、半透明的常驻触发角标/按钮。
    *   **内容区域 (`ContentArea`)**: 使用`androidx.fragment.app.FragmentContainerView` (id: `fragment_container_main`)，根据侧边栏的选择动态替换其中显示的`Fragment`。
*   **布局适配**: **所有XML布局文件在设计时，均需以横屏车载设备屏幕的主流分辨率和宽高比为基准进行优化，确保元素尺寸、间距、可点击区域适合大屏触摸操作，视觉清晰舒适。** (参考`car.html`原型)。
*   **播放状态同步**: 通过`ViewModel`与**统一播放服务**交互，确保UI能准确反映和控制播放状态。

---

## 6. 各功能视图详解 (布局与交互)

### 6.1. 播放器视图 (`PlayerFragment`)
*   **功能**: 展示当前播放歌曲的详细信息，提供完整的播放控制。
*   **布局 (`fragment_player.xml`)**: 无顶部标题栏。推荐使用`ConstraintLayout`进行灵活布局，或`LinearLayout`（横向，带权重）。
    *   **左侧区域 (例如占据40%宽度)**:
        *   `ImageView` (id: `imageview_player_album_art`): 尽可能大，正方形，显示专辑封面。
        *   `TextView` (id: `textview_player_song_title`): 歌曲名，字体较大，单行或允许两行跑马灯。
        *   `TextView` (id: `textview_player_artist_name`): 歌手名，字体适中。
    *   **右侧区域 (例如占据60%宽度)**:
        *   **歌词显示区**: 自定义`LyricsView`或`RecyclerView` (id: `recyclerview_player_lyrics`)，填充大部分右侧空间。支持当前播放行高亮，并随歌曲进度自动滚动。用户也可手动上下滑动查看歌词。
        *   **播放控制面板 (位于歌词区下方)**: 一组`ImageButton`，水平排列，间距合理。
            *   `ImageButton` (id: `button_player_playlist`): 图标 <i class="fas fa-list"></i>。
            *   `ImageButton` (id: `button_player_collect`): 图标 <i class="far fa-heart"></i> (未收藏) / <i class="fas fa-heart"></i> (已收藏)。
            *   `ImageButton` (id: `button_player_prev`): 图标 <i class="fas fa-step-backward"></i>。
            *   `ImageButton` (id: `button_player_play_pause`): 图标 <i class="fas fa-play-circle"></i> / <i class="fas fa-pause-circle"></i> (根据播放状态切换)，尺寸可稍大于其他控制按钮。
            *   `ImageButton` (id: `button_player_next`): 图标 <i class="fas fa-step-forward"></i>。
            *   `ImageButton` (id: `button_player_play_mode`): 图标根据当前播放模式动态切换 (例如 <i class="fas fa-random"></i> 随机, <i class="fas fa-redo-alt"></i> 单曲, <i class="fas fa-stream"></i> 顺序, <i class="fas fa-sync-alt"></i> 列表循环)。
        *   **进度条与时间 (位于控制面板下方或上方)**:
            *   `SeekBar` (id: `seekbar_player_progress`): 横向占满右侧控制区宽度。
            *   `TextView` (id: `textview_player_current_time`): 显示当前播放时间 (mm:ss)。
            *   `TextView` (id: `textview_player_total_time`): 显示歌曲总时长 (mm:ss)。
*   **交互**:
    *   UI通过`ViewModel`观察**统一播放服务**的状态更新：专辑封面、歌名、歌手、歌词（加载及同步高亮）、播放进度、总时长、播放/暂停状态（更新按钮图标）、收藏状态（更新按钮图标）、当前播放模式（更新按钮图标）。
    *   各控制按钮点击时，通过`ViewModel`调用统一播放服务的对应方法（如`playPause()`, `next()`, `previous()`, `toggleFavorite()`, `cyclePlayMode()`, `seekTo(progress)`）。
    *   拖动`SeekBar`时，实时更新UI上的当前时间，释放后调用服务`seekTo()`。
    *   点击播放列表按钮，可弹出`BottomSheetDialogFragment`或新的`Activity/Fragment`显示当前播放队列。

### 6.2. 我的音乐库视图 (`MusicLibraryFragment`)
*   **功能**: 管理和浏览用户的本地音乐、通过API获取的云端收藏（歌曲、专辑、歌手、歌单）。
*   **布局 (`fragment_music_library.xml`)**: 无顶部标题栏。主体为垂直`LinearLayout`。
    *   **顶部搜索框**: `androidx.appcompat.widget.SearchView` (id: `searchview_library_search`)，有搜索图标和提示文字（如"搜索我的音乐库..."）。
    *   **中部标签页**: `com.google.android.material.tabs.TabLayout` (id: `tablayout_library_categories`) 与 `androidx.viewpager2.widget.ViewPager2` (id: `viewpager_library_content`) 结合使用。
        *   Tabs: "本地音乐"、"歌曲"、"专辑"、"歌手"、"歌单"。
    *   **下部列表区 (ViewPager2的每个页面)**: 每个标签页对应一个独立的`Fragment` (例如 `LocalMusicListFragment`, `FavoriteSongListFragment`, `AlbumListFragment`, `ArtistListFragment`, `PlaylistListFragment`)。
        *   每个列表`Fragment`的核心是一个`RecyclerView` (id: `recyclerview_library_list_xxx`，xxx为类型)。
        *   **Item布局** (例如 `item_song_library.xml`, `item_album_library.xml`):
            *   `ImageView` (id: `imageview_item_thumbnail`): 显示歌曲封面、专辑封面、歌手头像或歌单封面。
            *   `TextView` (id: `textview_item_title`): 显示主标题（歌名、专辑名、歌手名、歌单名）。
            *   `TextView` (id: `textview_item_subtitle`): 显示副标题（歌手名、歌曲数量、创建者等）。
            *   `ImageButton` (id: `button_item_more_actions`): "更多操作"图标 (例如 <i class="fas fa-ellipsis-h"></i>)。
*   **交互**:
    *   `SearchView`监听文本变化，进行实时筛选（针对已加载的本地列表）或在用户提交搜索时触发API请求（针对云端数据，如果API支持库内搜索）。
    *   点击`TabLayout`的标签切换`ViewPager2`中显示的列表`Fragment`，加载对应数据。
    *   `RecyclerView`支持滚动，可能有下拉刷新和上拉加载更多（针对云端数据）。
    *   点击列表项的主要区域，将该项内容（歌曲、专辑的第一首歌、歌手的热门歌曲、歌单的第一首歌）交由**统一播放服务**开始播放。
    *   点击列表项的"更多操作"`ImageButton`，会弹出一个`BottomSheetDialog`或自定义`Dialog`，展示针对该项的可用操作。
        *   **API依赖和操作示例（需根据`api.txt`确认实际支持的接口）**:
            *   **本地歌曲**: 播放下一首, 添加到队列, 添加到云歌单 (需API), 查看信息, 从设备删除。
            *   **云端歌曲**: 播放下一首, 添加到队列, 取消收藏 (需API), 添加到歌单 (需API), 查看专辑详情, 查看歌手详情, 查看歌曲详情 (均需API)。
            *   **专辑**: 播放该专辑, 添加到队列, 收藏/取消收藏专辑 (均需API)。
            *   **歌手**: 播放该歌手的热门歌曲, 关注/取消关注歌手 (均需API)。
            *   **歌单**: 播放该歌单, 添加到队列, 删除歌单 (用户创建的, 需API), 收藏/取消收藏歌单 (推荐的, 需API)。
        *   **API缺失处理**: 若`api.txt`中未定义执行某个"更多操作"所需的接口，则该操作选项在菜单中不显示或置灰，并向开发者报告。
    *   所有云端数据的获取（各列表、搜索结果）及大部分"更多操作"的执行都**依赖API**。

### 6.3. 音乐探索视图 (`DiscoveryFragment`)
*   **功能**: 提供在线音乐发现渠道：个性化推荐、排行榜、新歌速递、热门专辑及在线搜索。
*   **布局 (`fragment_discovery.xml`)**: 无顶部标题栏。
    *   **顶部搜索框**: `androidx.appcompat.widget.SearchView` (id: `searchview_discovery_online_search`)，提示文字如"搜索歌曲、歌手、专辑..."。
    *   **内容滚动区**: 使用`androidx.core.widget.NestedScrollView`包裹一个垂直的`LinearLayout` (id: `linearlayout_discovery_sections_container`)，或者直接使用一个`RecyclerView` (id: `recyclerview_discovery_main_list`)，其Adapter能处理多种ViewType。
        *   **分区结构 (每个分区是一个独立的模块)**:
            *   **分区头部**: `LinearLayout`包含一个`TextView` (id: `textview_section_title_xxx`，显示分区标题如"专属推荐"、"热门榜单") 和一个可点击的`TextView`或`Button` (id: `button_section_see_more_xxx`，"查看更多 >")。
            *   **分区内容**:
                *   **卡片网格类型 (例如"专属推荐"、"热门专辑")**: 使用一个横向滚动的`RecyclerView` (id: `recyclerview_section_horizontal_grid_xxx`)。其内部使用`GridLayoutManager`（例如2行）来展示卡片。每个卡片布局 (`item_discovery_card_large.xml`)包含：`ImageView` (封面)、`TextView` (标题)、`TextView` (描述或播放量)。
                *   **列表/榜单类型 (例如"热门榜单"的歌曲预览，"新歌速递"的歌曲列表)**: 使用横向滚动的`RecyclerView` (id: `recyclerview_section_horizontal_list_xxx`) 展示简化的列表项 (`item_discovery_song_small.xml`)，或直接在垂直`LinearLayout`中嵌入几条记录。列表项包含：`ImageView` (小封面)、`TextView` (歌名)、`TextView` (歌手)。
*   **交互**:
    *   `SearchView`在用户输入并提交后，触发API进行在线音乐搜索，搜索结果可能会导航到一个新的搜索结果页/`Fragment`，或动态更新当前视图的某个区域。
    *   点击各分区的"查看更多"按钮，导航到该分类内容的完整列表页/`Fragment`（需API支持分页获取）。
    *   点击内容卡片或列表项的主要区域：通常是播放选定的内容（歌曲、歌单/专辑的第一首歌，通过统一播放服务）或进入其详情页（如专辑详情、歌单详情、完整榜单页）。
    *   若卡片或列表项提供"更多操作"按钮，则弹出`BottomSheetDialog`或`Dialog`，提供如"收藏到我的音乐库"（需API）、"添加到播放队列"、"添加到我的歌单"（需API）等选项。
    *   所有内容获取（推荐、榜单、新歌、专辑、搜索结果）及大部分交互操作都**依赖API**。若接口缺失，则对应分区不显示或显示占位/错误提示，并报告。

### 6.4. 驾驶模式视图 (`DrivingModeFragment`)
*   **功能**: 为驾驶场景优化的音乐播放界面，强调简洁易操作、大按钮和语音交互。
*   **布局 (`fragment_driving_mode.xml`)**: 无顶部标题栏。使用`ConstraintLayout`或权重`LinearLayout`进行左右分区。
    *   **视觉风格**: 深色背景，高对比度元素（如白色或亮蓝色文字/图标），字体和图标尺寸显著增大，元素间距拉开，确保点击区域足够大。
    *   **左侧主要播放信息与控制区 (例如占据60-70%宽度)**:
        *   大尺寸`ImageView` (id: `imageview_driving_album_art`): 显示专辑封面。
        *   大号`TextView` (id: `textview_driving_song_title`): 歌曲名，字体醒目。
        *   大号`TextView` (id: `textview_driving_artist_name`): 歌手名。
        *   粗大的`SeekBar` (id: `seekbar_driving_progress`): 显示和拖动播放进度。
        *   一组大尺寸的播放控制`ImageButton` (水平排列，间距大):
            *   `ImageButton` (id: `button_driving_prev`): 上一首。
            *   `ImageButton` (id: `button_driving_play_pause`): 播放/暂停（图标切换），尺寸应最大，最易点击。
            *   `ImageButton` (id: `button_driving_next`): 下一首。
    *   **右侧快捷操作区 (例如占据30-40%宽度)**: 使用`GridLayout` (id: `gridlayout_driving_quick_actions`)，例如2列2行。
        *   每个网格项是一个垂直的`LinearLayout`或自定义控件，包含一个大`ImageButton` (图标) 和其下方的`TextView` (文字标签)。
            *   操作1: `ImageButton` (id: `button_driving_voice_mode`), `TextView` ("语音模式")。图标 <i class="fas fa-microphone"></i>。
            *   操作2: `ImageButton` (id: `button_driving_playlist`), `TextView` ("播放列表")。图标 <i class="fas fa-list"></i>。
            *   操作3: `ImageButton` (id: `button_driving_play_order`), `TextView` ("播放顺序")。图标根据模式变化。
            *   操作4: `ImageButton` (id: `button_driving_volume`), `TextView` ("音量")。图标 <i class="fas fa-volume-up"></i>。
*   **交互**:
    *   **自动开启语音**: 当此Fragment变为可见时（例如在`onResume`生命周期方法中），会检查在设置中"进入驾驶模式时自动开启语音模式"项的状态。如果设置为开启，则自动调用逻辑激活语音识别监听。
    *   所有播放控制按钮的点击，通过`ViewModel`调用**统一播放服务**执行操作。
    *   播放信息（封面、歌名、歌手、进度、播放/暂停状态按钮图标）通过观察统一播放服务状态实时更新。
    *   点击"语音模式"快捷按钮，手动激活或关闭语音识别。
    *   点击"播放列表"，可能以简化的、字体超大的全屏列表`Dialog`或新`Fragment`形式展示当前播放队列，方便点选。
    *   点击"播放顺序"，循环切换播放模式（通过服务）。
    *   点击"音量"，可能会弹出一个大的、简易的音量调节滑块`Dialog`，或执行预设的音量增减步进。

### 6.5. 用户中心视图 (`UserProfileFragment`)
*   **功能**: 展示用户的个性化信息、音乐活动相关的统计数据和必要的账户详情。
*   **布局 (`fragment_user_profile.xml`)**: 无顶部标题栏。可使用`androidx.core.widget.NestedScrollView`包裹一个垂直的`LinearLayout`作为根布局。
    *   **头部区域 (Header)**:
        *   `ImageView` (id: `imageview_profile_cover_bg`): 作为背景，可以是模糊化的用户头像、预设图片或纯色/渐变。
        *   `com.google.android.material.imageview.ShapeableImageView` (id: `imageview_profile_avatar`, style设置为圆形): 尺寸较大（例如80-100dp），居中或偏左显示用户头像。
        *   `TextView` (id: `textview_profile_username`): 用户昵称，字体较大，位于头像下方或右侧。
        *   `LinearLayout` (id: `linearlayout_profile_tags`, optional): 水平排列显示用户标签（如VIP、等级），每个标签是一个带圆角背景的`TextView`。
        *   `TextView` (id: `textview_profile_signature`, optional): 个性签名，字体稍小。
    *   **统计数据区**: `com.google.android.material.card.MaterialCardView` (可选，或直接用`LinearLayout`)。内部使用一个水平的`LinearLayout` (id: `linearlayout_profile_stats_container`)，权重均分。
        *   每个统计项是一个垂直的`LinearLayout`，包含：
            *   `TextView` (id: `textview_profile_stat_value_xxx`): 大号字体显示数值（如"268"）。
            *   `TextView` (id: `textview_profile_stat_label_xxx`): 小号字体显示标签（如"收藏歌曲"）。
        *   统计项至少包括：收藏歌曲数、创建歌单数、关注歌手数、收听时长(小时)。
    *   **账户信息区**: `LinearLayout` (垂直，id: `linearlayout_profile_account_info`)。
        *   每一项信息是一个水平的`LinearLayout`，包含：
            *   `TextView` (id: `textview_profile_info_label_xxx`): 标签（如"手机号："、"邮箱："、"会员状态："、"注册时间："）。
            *   `TextView` (id: `textview_profile_info_value_xxx`): 对应的值（手机号可能部分掩码）。
        *   **此区域仅为信息展示，不提供"更多"入口或直接编辑功能。**
*   **交互**:
    *   页面数据主要在进入时通过`ViewModel`从API加载并填充UI。
    *   可以考虑加入下拉刷新功能 (`SwipeRefreshLayout`) 来重新获取最新的用户数据。
    *   所有动态信息（头像、昵称、标签、签名、统计数据、账户详情）的获取均**依赖API**。若接口缺失，对应部分显示占位符或提示信息加载失败，并报告。

### 6.6. 应用设置视图 (`SettingsFragment`)
*   **功能**: 提供应用级的核心配置选项。
*   **布局 (`fragment_settings.xml` 或在 `res/xml/settings_preferences.xml` 中定义)**: 无顶部标题栏。
    *   **推荐使用 `androidx.preference.PreferenceFragmentCompat`**。这使得设置界面的创建和管理更为简单和标准化。
        *   在 `res/xml/settings_preferences.xml` 中定义：
            *   `<SwitchPreferenceCompat app:key="setting_auto_play" app:title="自动播放" app:summary="启动应用时自动播放上次的歌曲"/>`
            *   `<SwitchPreferenceCompat app:key="setting_night_mode" app:title="夜间模式" app:summary="启用或关闭深色主题"/>`
            *   `<SwitchPreferenceCompat app:key="setting_auto_voice_in_driving" app:title="驾驶模式自动语音" app:summary="进入驾驶模式时自动开启语音识别"/>`
    *   如果不使用`PreferenceFragmentCompat`，则可以使用`RecyclerView`，其Adapter加载自定义的Item布局 (`item_setting_option.xml`)。该Item布局包含:
        *   `TextView` (id: `textview_setting_title`): 设置项标题。
        *   `TextView` (id: `textview_setting_summary`, optional): 设置项的简要描述。
        *   `com.google.android.material.switchmaterial.SwitchMaterial` (id: `switch_setting_toggle`): 开关控件。
*   **交互**:
    *   **自动播放**: `SwitchPreferenceCompat`或`SwitchMaterial`。状态改变时，将对应的布尔值保存到`SharedPreferences`。
    *   **夜间模式**: `SwitchPreferenceCompat`或`SwitchMaterial`。状态改变时，将选择（例如 `AppCompatDelegate.MODE_NIGHT_YES` / `AppCompatDelegate.MODE_NIGHT_NO` / `AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM`)保存到`SharedPreferences`，并调用`AppCompatDelegate.setDefaultNightMode()`来立即应用主题更改（可能需要`recreate()`当前Activity或所有Activity）。
    *   **进入驾驶模式时自动开启语音模式**: `SwitchPreferenceCompat`或`SwitchMaterial`。状态改变时，将布尔值保存到`SharedPreferences`。`DrivingModeFragment`的`onResume`会读取此设置值。

---

## 7. API 接口集成与数据处理
*   **API文档**: 所有后端API的调用方法、请求参数、响应数据结构，严格参考项目提供的 `api.txt` 文档。
*   **网络请求库**: 推荐使用 Retrofit + OkHttp。
*   **JSON解析**: 推荐使用 Gson 或 Moshi。
*   **开发前置检查**: 在开始开发任何依赖API的功能模块或具体操作前（例如我的音乐库中的云端列表获取、更多操作中的收藏/取消收藏，音乐探索的全部内容，用户中心的动态信息等），**必须首先仔细核对`api.txt`中是否存在对应的、定义清晰的接口。**
*   **接口缺失处理**: 若发现所需API接口在`api.txt`中未定义或定义不明确，**必须立即暂停该功能的具体编码实现，并向项目主导者或相关负责人明确报告所缺少的具体接口名称、预期功能以及它影响到的应用模块。在接口得到确认和提供之前，对应功能应标记为"待API支持"，UI上可能显示占位符、禁用相关操作或提示用户此功能暂不可用。**
*   **动态数据优先**: 歌曲信息、用户信息、推荐内容、封面图片等，**必须**优先尝试通过应用内部状态、用户数据缓存或实时调用API来获取并展示真实的动态数据。
*   **占位符策略**: 仅在真实数据确实无法获取（例如，API调用失败且无可用缓存，或特定数据字段为空且无合适默认值）的情况下，才可使用统一的、明确标记为临时的占位符（例如，通用的"无封面"图标，或显示"信息加载失败"、"暂无数据"等文本）。使用占位符时，务必在代码注释中说明原因。

---

## 8. UI/UX 设计与资源
*   **原型参考**: UI设计和布局应主要参考项目提供的 `car.html` **横屏原型** 与 **原型图**。
*   **欢迎页和登录页**: 这些页面的前端UI设计已完成，**不应**重新开发或进行过度修改，仅做集成和必要的逻辑绑定。
*   **资源文件**: 图标、图片等资源文件要求美观，并做好不同屏幕密度的适配（`drawable-mdpi`, `drawable-hdpi`, `drawable-xhdpi`, `drawable-xxhdpi`, `drawable-xxxhdpi`）。矢量图 (SVG/VectorDrawable) 优先。
*   **全屏模式**: 应用内所有界面均需通过代码设置取消系统底部导航栏和顶部状态栏（若有），实现真正的全屏沉浸式显示体验。
*   **用户体验**:
    *   在适当的地方（如数据加载、页面切换）使用加载动画（如`ProgressBar`）和过渡效果。
    *   确保应用在不同屏幕尺寸和横屏方向上都能提供响应式布局和良好体验。
    *   侧边导航栏的自动隐藏/滑出交互需流畅自然。

---

## 9. 性能与健壮性
*   **内存管理**: 务必关注内存泄漏问题（`Activity`/`Fragment`泄露、`Bitmap`未释放、监听器未移除、`Handler`回调等）。使用LeakCanary等工具进行检测。
*   **错误处理**:
    *   实现统一、规范的错误处理机制。
    *   对于用户操作可能发生的失败（如网络连接错误、API返回错误码、数据解析异常等），必须向用户提供友好且显眼的提示信息（如`Toast`、`Snackbar`或`Dialog`）。
    *   关键API调用失败时，应有重试机制或用户可操作的刷新功能。

---

## 10. GitHub 项目借鉴分析与主动学习
*   **`NeteaseCloudMusic-MVVM-master`**:
    *   **架构**: MVVM，值得我们项目采纳。
    *   **模块化**: `lib_audio` (核心播放逻辑), `lib_network` (网络请求), `lib_common_ui` 等模块化思路可借鉴，帮助分离关注点。
*   **`ponymusic-master`**:
    *   **专注性**: 纯音乐播放器，其本地音乐扫描、播放服务基础实现、通知栏交互等可参考。
*   **共同借鉴点**:
    *   **后台播放服务**: 两者都会有类似`UnifiedPlaybackService`的实现，可研究其媒体会话(`MediaSessionCompat`)管理、播放器引擎(ExoPlayer/MediaPlayer)使用、通知栏构建与交互、音频焦点处理。
    *   **UI与数据同步**: 观察它们如何通过`ViewModel`、`LiveData`或回调等机制同步播放状态到UI。
    *   **第三方库**: 留意它们使用的成熟第三方库（如播放器、网络、图片加载、JSON解析），可作为我们技术选型的参考。
    *   **主动学习与借鉴**: **在具体功能模块开发时（例如播放服务、播放器UI、列表展示、网络请求、用户认证流程等），应主动查阅这两个项目（尤其是`NeteaseCloudMusic-MVVM-master`的模块化实现和`ponymusic-master`的播放器核心功能）的对应源码，学习其设计模式、类结构、关键API用法、错误处理和性能优化技巧。这不是要求照搬代码，而是理解其解决问题的思路，并结合我们自身项目的需求进行适配和创新。**

---

## 11. AI 辅助开发要求
*   **文件完整性AI提示**: 在完成一个模块或主要功能的编码请求后，AI 应主动提示开发者检查是否所有必要的文件（Java 类、XML 布局、`Drawable` 资源、`Manifest` 修改等）都已按预期生成或更新。
*   **代码交付标准**:
    *   **完整代码**: AI 应避免使用省略号 (`...`) 或仅提供伪代码，除非开发者明确要求生成代码片段或思路。对于请求的类、方法或功能模块，应提供完整的实现骨架或具体的业务逻辑。
    *   **解释说明**: 对于复杂的逻辑实现、不常见的 API 用法或关键设计决策，AI 应主动提供简要的解释或在代码中添加注释。
    *   **借鉴说明**: **在生成复杂模块或核心功能的代码时，如果被问及或AI认为有益，可以主动提及从参考项目中借鉴的思路或模式，并简要说明为何这样借鉴以及如何适应当前项目的需求。**
*   **结构化反馈 (生成到独立的 .md 文件)**: 在交付主要代码块或模块后，AI 应提供一个总结性的 Markdown 文件，包含以下内容，以模拟开发者对代码的理解深度：
    *   整体功能和作用。
    *   UI 组件结构 (如适用)。
    *   状态管理和数据流 (特别是与统一播放服务的交互)。
    *   用户交互和事件处理。
    *   API 交互 (如适用), 包括对`api.txt`的依赖情况和**明确指出任何因API缺失而暂停或未实现的功能点**。
    *   关键依赖或技术栈。
    *   **呈现要求**: 反馈需清晰、结构化，侧重于架构和关键点的把握，避免详细的代码实现细节或逐行解释。

---

此文档已根据所有讨论和要求进行最终梳理。请以此为准进行开发。